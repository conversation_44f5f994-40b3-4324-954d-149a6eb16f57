# 🔄 配置文件迁移说明

## 📋 迁移概述

项目配置文件已从项目根目录迁移到 `backend/app/` 目录，并进行了分离：

### 迁移前
```
fast-gzmdrw-chat/
└── .env  # 单一配置文件
```

### 迁移后
```
fast-gzmdrw-chat/
├── backend/
│   └── app/
│       ├── .env                    # 主API服务配置
│       ├── data_pipeline.env       # 数据管道服务配置
│       └── data_pipeline_config_example.env  # 配置示例
└── .env  # 旧配置文件（删除）
```

## 🚀 新配置文件说明

### 1. 主API服务配置 (`backend/app/.env`)
包含主API服务所需的所有配置：
- OpenAI API配置
- 应用配置
- ChromaDB配置
- CORS配置

### 2. 数据管道服务配置 (`backend/app/data_pipeline.env`)
包含数据管道API服务所需的所有配置：
- 数据管道服务配置
- 数据库配置 (ChestnutCMS)
- 网站配置
- RAG服务配置
- 数据处理配置
- 日志配置
- 安全配置

### 3. 配置示例 (`backend/app/data_pipeline_config_example.env`)
数据管道配置的示例文件，包含所有必要的配置项和说明。

## 📝 迁移步骤

### 1. 复制配置示例
```bash
# 从项目根目录运行
cp backend/app/data_pipeline_config_example.env backend/app/data_pipeline.env
```

### 2. 编辑数据管道配置
编辑 `backend/app/data_pipeline.env` 文件，设置正确的数据库连接信息：

```env
# 数据库配置 (ChestnutCMS)
DB_USER=your_username
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=your_database_name
```

### 3. 验证配置
运行测试脚本验证配置是否正确：

```bash
# 从项目根目录运行
python backend/app/data_pipeline/test_apis.py
```

## 🔧 配置项说明

### 主API服务配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `OPENAI_API_KEY` | OpenAI API密钥 | 必需 |
| `OPENAI_BASE_URL` | OpenAI API基础URL | https://api.openai-proxy.org/v1 |
| `OPENAI_MODEL` | OpenAI模型名称 | gpt-4o-mini |
| `EMBEDDING_MODEL` | 嵌入模型名称 | text-embedding-3-small |
| `APP_HOST` | 应用主机地址 | 0.0.0.0 |
| `APP_PORT` | 应用端口 | 9000 |
| `DATA_DIR` | 数据目录 (相对于项目根目录) | ../../data |
| `STORAGE_DIR` | 存储目录 (相对于项目根目录) | ../../storage |
| `COLLECTION_NAME` | 集合名称 | documents |
| `CHROMA_PERSIST_DIRECTORY` | ChromaDB持久化目录 (相对于项目根目录) | ../../storage |
| `ALLOWED_ORIGINS` | 允许的CORS源 | ["http://localhost:3000", ...] |

### 数据管道服务配置项

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `DATA_PIPELINE_HOST` | 数据管道服务主机 | 0.0.0.0 |
| `DATA_PIPELINE_PORT` | 数据管道服务端口 | 9001 |
| `DB_USER` | 数据库用户名 | root |
| `DB_PASSWORD` | 数据库密码 | 必需 |
| `DB_HOST` | 数据库主机 | localhost |
| `DB_PORT` | 数据库端口 | 3306 |
| `DB_NAME` | 数据库名称 | chestnut_cms |
| `SITE_BASE_URL` | 网站基础URL | https://gzmdrw.cn |
| `RAG_SERVICE_URL` | RAG服务URL (指向端口9000) | http://localhost:9000 |
| `MAX_CONTENT_LENGTH` | 最大内容长度 | 10485760 |
| `SUPPORTED_FILE_TYPES` | 支持的文件类型 | .txt,.html,.htm |
| `CHUNK_SIZE` | 文档块大小 | 1000 |
| `CHUNK_OVERLAP` | 文档块重叠 | 200 |
| `LOG_LEVEL` | 日志级别 | INFO |
| `CORS_ORIGINS` | CORS源 | ["*"] |
| `RATE_LIMIT_PER_MINUTE` | 每分钟请求限制 | 100 |

## 📁 路径配置说明

### 相对路径配置

项目使用相对于项目根目录的路径配置，确保无论在哪里启动服务都能正确找到数据文件：

- **数据目录**: `../../data` → `fast-gzmdrw-chat/data/`
- **存储目录**: `../../storage` → `fast-gzmdrw-chat/storage/`
- **ChromaDB目录**: `../../storage` → `fast-gzmdrw-chat/storage/`

### 路径解析机制

配置文件中的相对路径会自动解析为项目根目录下的绝对路径：

```python
# 示例：如果项目根目录是 D:\AppProjects\ragapp\fast-gzmdrw-chat
# 那么 ../../storage 会被解析为：
# D:\AppProjects\ragapp\fast-gzmdrw-chat\storage
```

### 测试路径配置

运行以下命令测试路径配置是否正确：

```bash
# 从项目根目录运行
python backend/app/test_paths.py
```

## 🛠️ 故障排除

### 常见问题

1. **配置文件未找到**
   - 确保配置文件在正确的位置
   - 检查文件权限
   - 验证文件编码为UTF-8

2. **数据库连接失败**
   - 检查数据库配置是否正确
   - 确认MySQL服务正在运行
   - 验证数据库用户名和密码

3. **服务启动失败**
   - 检查配置文件语法
   - 确认所有必需配置项已设置
   - 查看服务日志获取详细错误信息

### 验证配置

运行以下命令验证配置：

```bash
# 测试主API服务配置
cd backend/app
python -c "from config.settings import settings; print('主API配置加载成功')"

# 测试数据管道服务配置
cd backend/app
python -c "import os; from dotenv import load_dotenv; load_dotenv('data_pipeline.env'); print('数据管道配置加载成功')"
```

## 📞 支持

如有问题，请：
1. 查看配置文件语法
2. 运行测试脚本
3. 检查服务日志
4. 参考API文档: `backend/app/data_pipeline/API_SERVICES_README.md`

---

**注意**: 
- 旧配置文件 `.env` 已保留，但不再使用
- 建议在迁移完成后备份旧配置文件
- 生产环境中请确保配置文件的安全性 