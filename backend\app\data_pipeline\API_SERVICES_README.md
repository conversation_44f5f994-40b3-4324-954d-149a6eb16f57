# RAG应用API服务说明

本项目包含两个独立的FastAPI服务，分别运行在不同的端口上，使用独立的配置文件：

## 🚀 服务概览

### 1. 主API服务 (端口9000)
- **文件**: `backend/app/main.py`
- **端口**: 9000
- **配置文件**: `backend/app/.env`
- **功能**: RAG聊天应用的核心服务，提供文档管理、问答查询等功能
- **CORS配置**: 推荐在 .env 文件中设置 `ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "https://gzmdrw.cn"]`

### 2. 数据管道API服务 (端口9001)
- **文件**: `backend/app/data_pipeline.py`
- **端口**: 9001
- **配置文件**: `backend/app/data_pipeline.env`
- **功能**: ChestnutCMS数据处理服务，提供文章管理、数据同步等功能
- **CORS配置**: 推荐在 data_pipeline.env 文件中设置 `CORS_ORIGINS=["http://localhost:9000", "http://127.0.0.1:9000", "https://gzmdrw.cn"]`

## 🛡️ 跨域（CORS）配置说明

为保证前端（如 https://gzmdrw.cn）能安全访问API服务，需在主API服务和数据管道API服务中配置允许的跨域来源。

- 主API服务：`ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "https://gzmdrw.cn"]`
- 数据管道API服务：`CORS_ORIGINS=["http://localhost:9000", "http://127.0.0.1:9000", "https://gzmdrw.cn"]`

生产环境请勿使用 `"*"`，而是明确指定受信任的域名。

## 📋 快速开始

### 配置文件设置

#### 1. 主API服务配置
```bash
# 配置文件位置: backend/app/.env
# 包含OpenAI API配置和应用配置
# 推荐添加：
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "https://gzmdrw.cn"]
```

#### 2. 数据管道服务配置
```bash
# 复制配置示例
cp backend/app/data_pipeline_config_example.env backend/app/data_pipeline.env

# 编辑配置文件，设置数据库连接信息
# 配置文件位置: backend/app/data_pipeline.env
# 推荐添加：
CORS_ORIGINS=["http://localhost:9000", "http://127.0.0.1:9000", "https://gzmdrw.cn"]
```

### 启动所有服务

使用启动脚本同时启动两个服务：

```bash
# 从项目根目录运行
python backend/app/data_pipeline/start_apis.py

# 或使用Windows批处理文件
start_services.bat
```

### 单独启动服务

#### 启动主API服务
```bash
cd backend/app
uvicorn main:app --host 0.0.0.0 --port 9000 --reload
```

#### 启动数据管道API服务
```bash
cd backend/app
uvicorn data_pipeline:app --host 0.0.0.0 --port 9001 --reload
```

### 测试服务

运行测试脚本验证服务状态：

```bash
# 从项目根目录运行
python backend/app/data_pipeline/test_apis.py

# 或使用Windows批处理文件
test_services.bat
```

## 🔗 服务地址

| 服务 | 地址 | 说明 |
|------|------|------|
| 主API服务 | http://localhost:9000 | RAG聊天应用主服务 |
| 主API文档 | http://localhost:9000/docs | Swagger API文档 |
| 数据管道API服务 | http://localhost:9001 | ChestnutCMS数据处理服务 |
| 数据管道API文档 | http://localhost:9001/docs | Swagger API文档 |

## 📚 API接口详细说明

### 主API服务接口 (端口9000) - RAG核心服务

#### 🏠 页面路由
- `GET /` - **聊天页面**
  - **功能**: 返回主聊天界面HTML页面
  - **返回**: HTMLResponse，包含完整的聊天界面
  - **文件**: `frontend/templates/index.html`
  - **特色**: 支持Markdown渲染的智能问答界面

- `GET /documents` - **文档管理页面**
  - **功能**: 返回文档管理界面HTML页面
  - **返回**: HTMLResponse，包含文档上传、删除、搜索功能
  - **文件**: `frontend/templates/documents.html`
  - **特色**: 可视化文档管理，支持批量操作

#### 🔍 系统状态接口
- `GET /api/status` - **获取系统状态**
  - **功能**: 返回RAG系统的运行状态和统计信息
  - **返回**: StatusResponse
    ```json
    {
      "status": "running",
      "documents_count": 5376,
      "storage_size": "125.6MB",
      "collection_name": "documents",
      "data_directory": "./data"
    }
    ```
  - **运行逻辑**:
    1. 检查RAG服务初始化状态
    2. 统计ChromaDB中的文档数量
    3. 计算data目录存储大小
    4. 返回系统配置信息

#### 📄 文档管理接口
- `GET /api/documents` - **获取文档列表**
  - **功能**: 获取RAG系统中所有已索引的文档信息
  - **返回**: DocumentsListResponse
    ```json
    {
      "success": true,
      "message": "获取文档列表成功",
      "documents": [
        {
          "filename": "example.txt",
          "chunks_count": 15,
          "file_size": 2048,
          "redirect_url": "https://example.com",
          "content_url": "https://example.com/content"
        }
      ],
      "total_chunks": 8520
    }
    ```
  - **运行逻辑**:
    1. 查询ChromaDB获取所有文档元数据
    2. 统计每个文档的块数量
    3. 获取文件大小信息
    4. 提取redirect_url和content_url
    5. 返回完整的文档列表

- `POST /api/documents/upload` - **上传文档**
  - **功能**: 上传TXT文件到RAG系统，支持同名文件替换
  - **参数**:
    - `file`: UploadFile (必需) - TXT文件
    - `redirect_url`: str (可选) - 重定向链接
    - `content_url`: str (可选) - 内容链接
  - **返回**: UploadDocumentResponse
    ```json
    {
      "success": true,
      "message": "文档上传成功",
      "filename": "example.txt",
      "replaced": false,
      "old_chunks": 0,
      "new_chunks": 15,
      "total_chunks": 8535
    }
    ```
  - **运行逻辑**:
    1. 验证文件格式（仅支持.txt）
    2. 检查是否存在同名文件，如存在则删除
    3. 将文件保存到data目录
    4. 使用LlamaIndex进行文档分块和向量化
    5. 存储到ChromaDB向量数据库
    6. 记录元数据（redirect_url, content_url等）
    7. 返回处理结果

- `DELETE /api/documents/{filename}` - **删除文档**
  - **功能**: 从RAG系统中删除指定文档
  - **参数**: `filename` - 文件名
  - **返回**: DeleteDocumentResponse
    ```json
    {
      "success": true,
      "message": "文档删除成功",
      "filename": "example.txt",
      "deleted_chunks": 15,
      "total_chunks": 8505
    }
    ```
  - **运行逻辑**:
    1. 从ChromaDB中删除文档的所有向量块
    2. 从data目录删除物理文件
    3. 重建查询引擎索引
    4. 返回删除统计信息

- `POST /api/load-documents` - **重新加载文档**
  - **功能**: 重新扫描data目录并重建索引
  - **返回**: LoadDocumentsResponse
    ```json
    {
      "success": true,
      "message": "文档重新加载完成",
      "documents_processed": 150,
      "replaced_files": ["old1.txt", "old2.txt"],
      "new_files": ["new1.txt", "new2.txt"],
      "processing_time": 45.6
    }
    ```
  - **运行逻辑**:
    1. 扫描data目录中的所有TXT文件
    2. 比较文件修改时间，识别新增和更新的文件
    3. 重新处理变更的文件
    4. 重建向量索引
    5. 返回处理统计

#### 🤖 问答查询接口
- `POST /api/query` - **智能问答查询**
  - **功能**: 基于RAG技术的智能问答，支持自定义提示词
  - **请求**: QueryRequest
    ```json
    {
      "query": "学校有哪些专业？",
      "max_results": 5,
      "similarity_threshold": 0.7
    }
    ```
  - **返回**: QueryResponse
    ```json
    {
      "answer": "根据文档信息，贵阳人文科技学院开设以下专业：...",
      "sources": [
        {
          "filename": "专业介绍.txt",
          "content": "相关文档片段内容...",
          "score": 0.85,
          "redirect_url": "https://gzmdrw.cn/zhuanye",
          "content_url": "https://gzmdrw.cn/content/123"
        }
      ],
      "processing_time": 2.3,
      "total_sources": 3
    }
    ```
  - **运行逻辑**:
    1. 接收用户问题和查询参数
    2. 使用向量相似度搜索相关文档片段
    3. 应用自定义提示词模板（针对贵阳人文科技学院优化）
    4. 调用LLM生成结构化答案
    5. 返回答案和相关源文档信息（包含URL链接）

### 数据管道API服务接口 (端口9001) - CMS数据同步服务

#### 🏠 基础接口
- `GET /` - **服务健康检查**
  - **功能**: 返回数据管道服务状态和数据库连接信息
  - **返回**:
    ```json
    {
      "message": "Welcome to ChestnutCMS API",
      "status": "running",
      "database_connected": true,
      "database_url": "mysql://localhost:3306/cms_db",
      "rag_service_url": "http://localhost:9000"
    }
    ```

- `GET /status` - **获取服务状态**
  - **功能**: 详细的服务状态信息
  - **返回**: 包含服务版本、数据库状态、配置信息等

#### 🔧 调试接口
- `GET /debug/rag-connection` - **测试RAG服务连接**
  - **功能**: 全面测试与RAG服务的连接状态
  - **返回**:
    ```json
    {
      "rag_service_url": "http://localhost:9000",
      "connection_test": {
        "health_check": {"status": "success", "response_time": 0.05},
        "status_api": {"status": "success", "documents_count": 5376},
        "documents_api": {"status": "success", "documents_count": 5376}
      }
    }
    ```
  - **运行逻辑**:
    1. 测试RAG服务根路径连通性
    2. 测试/api/status接口响应
    3. 测试/api/documents接口并获取文档数量
    4. 计算响应时间
    5. 返回详细的连接测试结果

- `GET /debug/sync-status` - **同步状态调试**
  - **功能**: 对比CMS和RAG数据库的差异，便于同步调试
  - **返回**:
    ```json
    {
      "rag_service_available": true,
      "cms_articles_count": 6698,
      "cms_valid_articles_count": 6588,
      "cms_invalid_articles_count": 110,
      "rag_documents_count": 5376,
      "to_add_count": 95,
      "to_delete_count": 12,
      "common_count": 5281,
      "sample_invalid_articles": [
        {"id": 310000000000131, "title": "头条"}
      ]
    }
    ```
  - **运行逻辑**:
    1. 统计CMS中的所有文章（有效/无效分类）
    2. 获取RAG中的文档列表
    3. 比较文件名差异，计算待新增、待删除数量
    4. 识别无效文章（既无正文也无链接）
    5. 返回详细的同步状态分析

#### 📄 文章管理接口
- `GET /articles/to-add` - **获取待添加文章列表**
  - **功能**: 获取CMS中存在但RAG中不存在的文章，或需要更新的文章
  - **返回**: List[ArticleResponse]
    ```json
    [
      {
        "content_id": 310000000003202,
        "title": "【简介】科研处简介",
        "content_type": "article",
        "publish_date": "2023-10-01T08:22:23",
        "update_time": "2023-10-01T08:22:23",
        "redirect_url": null,
        "content_url": "https://gzmdrw.cn/content/310000000003202",
        "action": "add",
        "filename": "【简介】科研处简介.txt",
        "chunks_count": 0,
        "file_size": null
      }
    ]
    ```
  - **运行逻辑**:
    1. 获取CMS中所有文章
    2. 获取RAG中的文档列表
    3. 比较文件名，找出CMS有但RAG没有的文章
    4. 使用Unix时间戳比较，找出需要更新的文章
    5. 过滤掉无效文章（既无正文也无链接）
    6. 为每篇文章生成content_url
    7. 返回待处理文章列表和统计信息

- `GET /articles/to-delete` - **获取待删除文章列表**
  - **功能**: 获取RAG中存在但CMS中不存在的文档
  - **返回**: List[ArticleResponse]
  - **运行逻辑**:
    1. 获取RAG中的所有文档
    2. 获取CMS中的所有文章标题
    3. 比较差异，找出RAG有但CMS没有的文档
    4. 返回需要删除的文档列表

- `GET /articles/{article_id}/txt` - **获取文章纯文本内容**
  - **功能**: 根据文章ID获取纯文本内容，支持多种内容源
  - **参数**: `article_id` - 文章ID
  - **返回**: StreamingResponse (text/plain)
  - **运行逻辑**:
    1. 查询CMS数据库获取文章基本信息
    2. 查询文章详情表获取HTML正文
    3. **内容优先级处理**:
       - **第一优先级**: 正文内容（HTML转纯文本）
       - **第二优先级**: 重定向链接（格式化显示）
       - **第三优先级**: 文章标题（友好提示，避免404）
    4. 设置正确的文件名和编码
    5. 返回流式文本响应
  - **改进特性**:
    - ✅ **消除404错误**: 无内容时返回标题而非404
    - ✅ **智能内容处理**: 根据可用内容自动选择最佳展示方式
    - ✅ **详细日志**: 记录内容来源和处理过程

- `GET /articles/` - **下载所有文章**
  - **功能**: 批量下载所有文章为ZIP压缩包
  - **返回**: StreamingResponse (application/zip)
  - **运行逻辑**:
    1. 查询所有文章
    2. 为每篇文章生成TXT文件
    3. 创建ZIP压缩包
    4. 返回流式下载响应

- `GET /articles/summary` - **获取文章摘要统计**
  - **功能**: 获取文章的统计信息和摘要
  - **返回**: 文章统计数据
  - **运行逻辑**:
    1. 统计所有文章数量
    2. 分析文章类型分布
    3. 计算同步状态
    4. 返回摘要信息

## 🔄 核心业务逻辑详解

### 1. 无效文章处理机制
**问题**: 部分文章既没有正文内容也没有重定向链接，导致404错误

**解决方案**:
```python
def is_valid_article(article, article_detail=None) -> bool:
    """判断文章是否有效"""
    has_redirect = bool(article.redirect_url and article.redirect_url.strip())
    has_content = False
    if article_detail and hasattr(article_detail, 'content_html'):
        has_content = bool(article_detail.content_html and article_detail.content_html.strip())
    return has_content or has_redirect
```

**处理流程**:
1. 在`/articles/to-add`中过滤无效文章
2. 在统计中显示"无效文章"而非"操作失败"
3. 在`/articles/{article_id}/txt`中返回标题而非404

### 2. 时间比较优化机制
**问题**: 原有DateTime比较存在精度和时区问题

**解决方案**: Unix时间戳比较
```python
# CMS更新时间转时间戳
local_timestamp = local_time.timestamp()
# RAG写入时间戳
doc_timestamp = float(doc_time_str)
# 精确比较
if local_timestamp > doc_timestamp:
    to_update_filenames.add(filename)
```

**优势**:
- ✅ 微秒级精度
- ✅ 避免时区问题
- ✅ 性能更好
- ✅ 完善的错误处理

### 3. 同步链路详解
**数据流向**: CMS → 数据管道 → RAG服务 → 向量数据库

**同步步骤**:
1. **发现阶段**: `/articles/to-add` 比较CMS和RAG差异
2. **内容获取**: `/articles/{article_id}/txt` 获取文章内容
3. **上传处理**: `/api/documents/upload` 上传到RAG系统
4. **索引更新**: RAG服务更新向量索引和物理文件

**错误处理**:
- RAG服务不可用时返回503错误而非误判
- 无效文章跳过处理并统计
- 详细的日志记录便于调试

## 💡 使用示例

### 1. 完整的同步流程示例

```python
import requests
import time

def complete_sync_workflow():
    """完整的CMS到RAG同步流程"""

    print("🚀 开始完整同步流程...")

    # 1. 检查服务状态
    print("\n1️⃣ 检查服务状态")
    rag_status = requests.get("http://localhost:9000/api/status")
    pipeline_status = requests.get("http://localhost:9001/status")

    if rag_status.status_code == 200 and pipeline_status.status_code == 200:
        print("✅ 所有服务正常运行")
        rag_info = rag_status.json()
        print(f"   RAG文档数量: {rag_info['documents_count']}")
    else:
        print("❌ 服务状态异常，请检查")
        return

    # 2. 测试RAG连接
    print("\n2️⃣ 测试RAG服务连接")
    connection_test = requests.get("http://localhost:9001/debug/rag-connection")
    if connection_test.status_code == 200:
        debug_info = connection_test.json()
        print(f"✅ RAG连接正常: {debug_info['rag_service_url']}")
        print(f"   文档API状态: {debug_info['connection_test']['documents_api']['status']}")
    else:
        print("❌ RAG连接失败")
        return

    # 3. 获取同步状态
    print("\n3️⃣ 分析同步状态")
    sync_status = requests.get("http://localhost:9001/debug/sync-status")
    if sync_status.status_code == 200:
        status_info = sync_status.json()
        print(f"   CMS文章总数: {status_info['cms_articles_count']}")
        print(f"   CMS有效文章: {status_info['cms_valid_articles_count']}")
        print(f"   CMS无效文章: {status_info['cms_invalid_articles_count']}")
        print(f"   RAG文档数量: {status_info['rag_documents_count']}")
        print(f"   待新增文章: {status_info['to_add_count']}")
        print(f"   待删除文档: {status_info['to_delete_count']}")

    # 4. 获取待处理文章
    print("\n4️⃣ 获取待处理文章")
    to_add_response = requests.get("http://localhost:9001/articles/to-add")
    if to_add_response.status_code == 200:
        articles_to_add = to_add_response.json()
        print(f"✅ 获取到 {len(articles_to_add)} 篇待处理文章")

        # 5. 处理前几篇文章作为示例
        print("\n5️⃣ 处理示例文章")
        for i, article in enumerate(articles_to_add[:3]):  # 只处理前3篇作为示例
            print(f"\n   处理文章 {i+1}: {article['title']}")

            # 获取文章内容
            content_response = requests.get(f"http://localhost:9001/articles/{article['content_id']}/txt")
            if content_response.status_code == 200:
                content = content_response.text
                print(f"   ✅ 获取内容成功，长度: {len(content)} 字符")

                # 上传到RAG系统
                files = {'file': (article['filename'], content, 'text/plain')}
                data = {
                    'redirect_url': article.get('redirect_url'),
                    'content_url': article.get('content_url')
                }

                upload_response = requests.post(
                    "http://localhost:9000/api/documents/upload",
                    files=files,
                    data=data
                )

                if upload_response.status_code == 200:
                    upload_info = upload_response.json()
                    print(f"   ✅ 上传成功: {upload_info['message']}")
                    print(f"      新增块数: {upload_info['new_chunks']}")
                else:
                    print(f"   ❌ 上传失败: {upload_response.status_code}")
            else:
                print(f"   ❌ 获取内容失败: {content_response.status_code}")

            time.sleep(1)  # 避免请求过快

    print("\n🎉 同步流程演示完成！")

# 运行完整流程
complete_sync_workflow()
```

### 2. 智能问答测试示例

```python
import requests
import json

def test_intelligent_qa():
    """测试智能问答功能"""

    test_questions = [
        "学校有哪些专业？",
        "如何申请助学金？",
        "宿舍条件怎么样？",
        "学费是多少？",
        "如何联系招生办？"
    ]

    print("🤖 测试智能问答功能")
    print("=" * 50)

    for i, question in enumerate(test_questions, 1):
        print(f"\n{i}. 问题: {question}")

        # 发送问答请求
        query_data = {
            "query": question,
            "max_results": 3,
            "similarity_threshold": 0.7
        }

        response = requests.post(
            "http://localhost:9000/api/query",
            json=query_data,
            headers={"Content-Type": "application/json"}
        )

        if response.status_code == 200:
            result = response.json()
            print(f"   ✅ 回答: {result['answer'][:100]}...")
            print(f"   📊 处理时间: {result['processing_time']:.2f}秒")
            print(f"   📚 参考源数量: {result['total_sources']}")

            # 显示源文档信息
            for j, source in enumerate(result['sources'][:2]):  # 只显示前2个源
                print(f"   📄 源{j+1}: {source['filename']} (相似度: {source['score']:.2f})")
                if source.get('redirect_url'):
                    print(f"        🔗 链接: {source['redirect_url']}")
        else:
            print(f"   ❌ 查询失败: {response.status_code}")

        print("-" * 30)

# 运行问答测试
test_intelligent_qa()
```

### 3. 文档管理操作示例

```python
import requests
import os

def document_management_demo():
    """文档管理操作演示"""

    print("📁 文档管理操作演示")
    print("=" * 40)

    # 1. 获取当前文档列表
    print("\n1️⃣ 获取文档列表")
    docs_response = requests.get("http://localhost:9000/api/documents")
    if docs_response.status_code == 200:
        docs_info = docs_response.json()
        print(f"   📊 当前文档数量: {len(docs_info['documents'])}")
        print(f"   📊 总文档块数: {docs_info['total_chunks']}")

        # 显示前5个文档
        for i, doc in enumerate(docs_info['documents'][:5]):
            print(f"   📄 {i+1}. {doc['filename']} ({doc['chunks_count']} 块)")

    # 2. 上传新文档示例
    print("\n2️⃣ 上传文档示例")

    # 创建示例文档
    sample_content = """
贵阳人文科技学院测试文档

这是一个测试文档，用于演示文档上传功能。

学校特色：
1. 优质的教学环境
2. 丰富的专业设置
3. 完善的设施配备

联系方式：
电话: 0851-88202182
网址: https://gzmdrw.cn
"""

    # 上传文档
    files = {'file': ('测试文档.txt', sample_content, 'text/plain')}
    data = {
        'redirect_url': 'https://gzmdrw.cn/test',
        'content_url': 'https://gzmdrw.cn/content/test'
    }

    upload_response = requests.post(
        "http://localhost:9000/api/documents/upload",
        files=files,
        data=data
    )

    if upload_response.status_code == 200:
        upload_info = upload_response.json()
        print(f"   ✅ 上传成功: {upload_info['filename']}")
        print(f"   📊 新增块数: {upload_info['new_chunks']}")

        # 3. 测试查询新上传的文档
        print("\n3️⃣ 测试查询新文档")
        query_data = {
            "query": "学校有什么特色？",
            "max_results": 3
        }

        query_response = requests.post(
            "http://localhost:9000/api/query",
            json=query_data
        )

        if query_response.status_code == 200:
            result = query_response.json()
            print(f"   ✅ 查询成功，找到 {result['total_sources']} 个相关源")
            print(f"   📝 回答: {result['answer'][:150]}...")

        # 4. 删除测试文档
        print("\n4️⃣ 清理测试文档")
        delete_response = requests.delete(f"http://localhost:9000/api/documents/测试文档.txt")
        if delete_response.status_code == 200:
            delete_info = delete_response.json()
            print(f"   ✅ 删除成功: {delete_info['filename']}")
            print(f"   📊 删除块数: {delete_info['deleted_chunks']}")
    else:
        print(f"   ❌ 上传失败: {upload_response.status_code}")

# 运行文档管理演示
document_management_demo()
```

### 4. 错误处理和调试示例

```python
import requests

def error_handling_demo():
    """错误处理和调试示例"""

    print("🔧 错误处理和调试演示")
    print("=" * 40)

    # 1. 测试无效文章处理
    print("\n1️⃣ 测试无效文章处理")

    # 尝试获取一个不存在的文章
    invalid_response = requests.get("http://localhost:9001/articles/999999999/txt")
    print(f"   不存在文章响应: {invalid_response.status_code}")

    # 获取调试状态
    debug_response = requests.get("http://localhost:9001/debug/sync-status")
    if debug_response.status_code == 200:
        debug_info = debug_response.json()
        print(f"   📊 无效文章数量: {debug_info['cms_invalid_articles_count']}")

        if debug_info.get('sample_invalid_articles'):
            print("   📄 无效文章示例:")
            for article in debug_info['sample_invalid_articles'][:3]:
                print(f"      - ID: {article['id']}, 标题: {article['title']}")

    # 2. 测试服务连接状态
    print("\n2️⃣ 测试服务连接状态")

    services = [
        ("RAG主服务", "http://localhost:9000/api/status"),
        ("数据管道服务", "http://localhost:9001/status"),
        ("RAG连接测试", "http://localhost:9001/debug/rag-connection")
    ]

    for service_name, url in services:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {service_name}: 正常")
            else:
                print(f"   ⚠️ {service_name}: HTTP {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ {service_name}: 连接失败 - {e}")

    # 3. 测试数据一致性
    print("\n3️⃣ 测试数据一致性")

    try:
        # 获取RAG文档数量
        rag_docs = requests.get("http://localhost:9000/api/documents")
        rag_count = len(rag_docs.json()['documents']) if rag_docs.status_code == 200 else 0

        # 获取CMS文章数量
        sync_status = requests.get("http://localhost:9001/debug/sync-status")
        cms_count = sync_status.json()['cms_valid_articles_count'] if sync_status.status_code == 200 else 0

        print(f"   📊 RAG文档数量: {rag_count}")
        print(f"   📊 CMS有效文章数量: {cms_count}")
        print(f"   📊 差异: {abs(cms_count - rag_count)}")

        if abs(cms_count - rag_count) > 100:
            print("   ⚠️ 数据差异较大，建议检查同步状态")
        else:
            print("   ✅ 数据基本一致")

    except Exception as e:
        print(f"   ❌ 数据一致性检查失败: {e}")

# 运行错误处理演示
error_handling_demo()
```

## 🔧 配置说明

### 主API服务配置 (`backend/app/.env`)
```env
# OpenAI API配置
OPENAI_API_KEY=your_api_key
OPENAI_BASE_URL=https://api.openai-proxy.org/v1
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=9000
DATA_DIR=./data
STORAGE_DIR=./storage
COLLECTION_NAME=documents

# ChromaDB配置
CHROMA_PERSIST_DIRECTORY=./storage

# CORS配置
ALLOWED_ORIGINS=["http://localhost:3000", "http://127.0.0.1:3000", "https://gzmdrw.cn"]
```

### 数据管道API服务配置 (`backend/app/data_pipeline.env`)
```env
# 数据管道API服务配置
DATA_PIPELINE_HOST=0.0.0.0
DATA_PIPELINE_PORT=9001

# 数据库配置 (ChestnutCMS)
DB_USER=your_username
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=your_database_name

# 网站配置
SITE_BASE_URL=https://gzmdrw.cn

# RAG服务配置 - 指向本项目的端口9000
RAG_SERVICE_URL=http://localhost:9000

# 数据处理配置
MAX_CONTENT_LENGTH=10485760
SUPPORTED_FILE_TYPES=.txt,.html,.htm
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 安全配置
CORS_ORIGINS=["http://localhost:9000", "http://127.0.0.1:9000", "https://gzmdrw.cn"]
RATE_LIMIT_PER_MINUTE=100
```

## 📁 目录结构

```
fast-gzmdrw-chat/
├── backend/
│   ├── app/
│   │   ├── .env                        # 主API服务配置（含CORS）
│   │   ├── data_pipeline.env           # 数据管道服务配置（含CORS）
│   │   ├── data_pipeline_config_example.env # 配置示例
│   │   ├── main.py                     # 主API服务
│   │   ├── data_pipeline.py            # 数据管道API服务
│   │   ├── rag_service.py              # RAG服务
│   │   └── data_pipeline/              # 数据管道相关文件
│   │       ├── start_apis.py           # 启动脚本
│   │       ├── test_apis.py            # 测试脚本
│   │       ├── example_usage.py        # 使用示例
│   │       ├── start_apis.bat          # Windows启动脚本
│   │       ├── test_apis.bat           # Windows测试脚本
│   │       ├── API_SERVICES_README.md  # API文档
│   │       ├── QUICK_START.md          # 快速启动指南
│   │       └── data_pipeline_config_example.env # 配置示例
│   └── config/
│       └── settings.py                 # 配置文件
├── data/                               # 数据目录
├── storage/                            # 存储目录
├── .env                               # 旧配置文件（已迁移）
├── requirements.txt                   # Python依赖
└── README.md                          # 项目主文档
```

## 🛠️ 故障排除

### 常见问题及解决方案

#### 1. 服务启动问题

**端口被占用**
```bash
# Windows查看端口占用
netstat -ano | findstr :9000
netstat -ano | findstr :9001

# 杀死占用进程
taskkill /PID <进程ID> /F

# Linux/Mac查看端口占用
lsof -i :9000
lsof -i :9001

# 杀死占用进程
kill -9 <进程ID>
```

**服务启动失败**
```bash
# 检查Python环境
python --version
pip list | grep fastapi

# 检查配置文件
ls -la backend/app/.env
ls -la backend/app/data_pipeline.env

# 查看详细错误日志
cd backend/app
python -m uvicorn main:app --host 0.0.0.0 --port 9000 --log-level debug
python -m uvicorn data_pipeline:app --host 0.0.0.0 --port 9001 --log-level debug
```

#### 2. 数据库连接问题

**MySQL连接失败**
```python
# 测试数据库连接
import mysql.connector
from mysql.connector import Error

try:
    connection = mysql.connector.connect(
        host='localhost',
        database='your_database',
        user='your_username',
        password='your_password'
    )
    if connection.is_connected():
        print("✅ 数据库连接成功")
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM cms_content WHERE content_type='article'")
        count = cursor.fetchone()[0]
        print(f"📊 文章数量: {count}")
except Error as e:
    print(f"❌ 数据库连接失败: {e}")
finally:
    if connection.is_connected():
        connection.close()
```

**ChromaDB问题**
```bash
# 检查ChromaDB存储目录
ls -la ./storage/

# 重建ChromaDB索引
rm -rf ./storage/chroma.sqlite3
# 重启RAG服务，会自动重建索引
```

#### 3. API调用问题

**CORS跨域问题**
```python
# 检查CORS配置
import requests

# 测试预检请求
headers = {
    'Origin': 'https://gzmdrw.cn',
    'Access-Control-Request-Method': 'POST',
    'Access-Control-Request-Headers': 'Content-Type'
}

response = requests.options("http://localhost:9000/api/query", headers=headers)
print(f"CORS预检状态: {response.status_code}")
print(f"允许的源: {response.headers.get('Access-Control-Allow-Origin')}")
```

**请求格式错误**
```python
# 正确的请求格式示例
import requests
import json

# 正确的JSON请求
correct_request = {
    "query": "学校有哪些专业？",
    "max_results": 5,
    "similarity_threshold": 0.7
}

response = requests.post(
    "http://localhost:9000/api/query",
    json=correct_request,  # 使用json参数
    headers={"Content-Type": "application/json"}
)

print(f"响应状态: {response.status_code}")
if response.status_code != 200:
    print(f"错误信息: {response.text}")
```

#### 4. 同步问题

**文章同步失败**
```python
# 诊断同步问题
import requests

def diagnose_sync_issues():
    print("🔍 诊断同步问题...")

    # 1. 检查服务状态
    services_ok = True
    for service, url in [("RAG", "http://localhost:9000/api/status"),
                        ("Pipeline", "http://localhost:9001/status")]:
        try:
            resp = requests.get(url, timeout=5)
            if resp.status_code == 200:
                print(f"✅ {service}服务正常")
            else:
                print(f"❌ {service}服务异常: {resp.status_code}")
                services_ok = False
        except Exception as e:
            print(f"❌ {service}服务连接失败: {e}")
            services_ok = False

    if not services_ok:
        return

    # 2. 检查数据一致性
    sync_status = requests.get("http://localhost:9001/debug/sync-status")
    if sync_status.status_code == 200:
        data = sync_status.json()
        print(f"📊 CMS文章: {data['cms_articles_count']}")
        print(f"📊 RAG文档: {data['rag_documents_count']}")
        print(f"📊 待新增: {data['to_add_count']}")
        print(f"📊 无效文章: {data['cms_invalid_articles_count']}")

        if data['to_add_count'] > 1000:
            print("⚠️ 待新增文章过多，可能是首次同步")
        elif data['to_add_count'] == 0:
            print("✅ 数据已同步")

    # 3. 测试单个文章处理
    to_add = requests.get("http://localhost:9001/articles/to-add")
    if to_add.status_code == 200 and to_add.json():
        test_article = to_add.json()[0]
        print(f"🧪 测试文章: {test_article['title']}")

        # 测试内容获取
        content_resp = requests.get(f"http://localhost:9001/articles/{test_article['content_id']}/txt")
        if content_resp.status_code == 200:
            print(f"✅ 内容获取成功: {len(content_resp.text)} 字符")
        else:
            print(f"❌ 内容获取失败: {content_resp.status_code}")

diagnose_sync_issues()
```

#### 5. 性能问题

**查询响应慢**
```python
# 性能测试
import requests
import time

def performance_test():
    questions = [
        "学校简介",
        "专业设置",
        "招生信息",
        "联系方式",
        "学费标准"
    ]

    total_time = 0
    for i, question in enumerate(questions):
        start_time = time.time()

        response = requests.post(
            "http://localhost:9000/api/query",
            json={"query": question, "max_results": 3}
        )

        end_time = time.time()
        query_time = end_time - start_time
        total_time += query_time

        if response.status_code == 200:
            result = response.json()
            print(f"查询 {i+1}: {query_time:.2f}s (服务器: {result['processing_time']:.2f}s)")
        else:
            print(f"查询 {i+1}: 失败 ({response.status_code})")

    avg_time = total_time / len(questions)
    print(f"平均响应时间: {avg_time:.2f}s")

    if avg_time > 5:
        print("⚠️ 响应时间较慢，建议检查:")
        print("   - 文档数量是否过多")
        print("   - similarity_top_k 是否设置过高")
        print("   - 服务器资源是否充足")

performance_test()
```

### 数据库配置

数据管道API服务需要MySQL数据库连接，请确保在 `backend/app/data_pipeline.env` 文件中配置以下变量：

```env
DB_USER=your_username
DB_PASSWORD=your_password
DB_HOST=localhost
DB_PORT=3306
DB_NAME=your_database_name
```

### 日志查看

服务启动时会显示详细的日志信息，包括：
- 服务启动状态
- 数据库连接状态
- API请求日志
- 错误信息

## 📞 支持

如有问题，请：
1. 查看服务日志
2. 运行测试脚本
3. 检查配置文件
4. 确认数据库连接
5. 检查网络连接

---

**注意**: 在生产环境中，请确保：
- 配置适当的安全设置
- 限制CORS访问
- 使用HTTPS
- 设置适当的日志级别
- 保护数据库连接信息 

## 同步链路与调试

### 分工说明
- 数据管道（data_pipeline.py）负责CMS与RAG服务之间的同步、比对、调度。
- RAG服务（main.py）负责本地data目录与向量数据库的文档管理。

### 同步流程
1. 前端调用 `/articles/to-add` 获取所有待同步文章（不再限制正文内容）。
2. 依次请求 `/articles/{article_id}/txt` 获取txt内容。
3. 拿到内容后，调用 `/api/documents/upload` 上传。
4. 上传成功后，data目录和RAG数据库同步更新。

### 常见异常与调试建议
- RAG数据库有文档但data目录无文件：多为RAG服务写文件异常被吞掉，需检查rag_service.py的文件写入逻辑和DATA_DIR配置。
- /articles/to-add返回的文章无法同步：多为正文和redirect_url都为空，/articles/{article_id}/txt会404。
- /debug/sync-status 可一键比对CMS与RAG的所有差异，定位缺失文件。
- 建议在RAG服务的文件写入处加详细日志，确保异常能反馈到前端。 

## 📈 性能优化建议

### 1. RAG服务优化
```python
# 在 backend/config/settings.py 中调整参数
similarity_top_k = 3  # 减少检索文档数量提升速度
temperature = 0.1     # 降低温度提高回答一致性
max_tokens = 800      # 限制回答长度
```

### 2. 数据库优化
```sql
-- 为常用查询字段添加索引
CREATE INDEX idx_content_type ON cms_content(content_type);
CREATE INDEX idx_update_time ON cms_content(update_time);
CREATE INDEX idx_content_id ON cms_article_detail(content_id);
```

### 3. 缓存策略
```python
# 可考虑添加Redis缓存常用查询结果
import redis
import json

redis_client = redis.Redis(host='localhost', port=6379, db=0)

def cached_query(question, cache_time=3600):
    cache_key = f"rag_query:{hash(question)}"
    cached_result = redis_client.get(cache_key)

    if cached_result:
        return json.loads(cached_result)

    # 执行实际查询
    result = actual_query(question)

    # 缓存结果
    redis_client.setex(cache_key, cache_time, json.dumps(result))
    return result
```

## 🔒 安全最佳实践

### 1. CORS配置
```env
# 生产环境严格配置
ALLOWED_ORIGINS=["https://gzmdrw.cn"]
CORS_ORIGINS=["https://gzmdrw.cn"]

# 开发环境可以包含本地地址
ALLOWED_ORIGINS=["https://gzmdrw.cn", "http://localhost:3000", "http://127.0.0.1:3000"]
```

### 2. 数据库安全
```env
# 使用强密码
DB_PASSWORD=your_strong_password_here

# 限制数据库访问IP
DB_HOST=127.0.0.1  # 仅本地访问

# 使用SSL连接（如果支持）
DB_SSL_MODE=require
```

### 3. API安全
```python
# 添加请求频率限制
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address

limiter = Limiter(key_func=get_remote_address)

@app.post("/api/query")
@limiter.limit("10/minute")  # 每分钟最多10次查询
async def query_documents(request: Request, query_request: QueryRequest):
    # 查询逻辑
    pass
```

### 4. 日志安全
```python
# 避免在日志中记录敏感信息
import logging

# 配置安全的日志格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('app.log'),
        logging.StreamHandler()
    ]
)

# 避免记录完整的用户查询内容
logger.info(f"收到查询请求，长度: {len(query)}")  # 而不是记录完整查询
```

## 📊 监控和维护

### 1. 健康检查脚本
```python
#!/usr/bin/env python3
"""
系统健康检查脚本
定期运行以监控系统状态
"""

import requests
import smtplib
from email.mime.text import MIMEText
import time

def health_check():
    """执行健康检查"""
    issues = []

    # 检查服务状态
    services = [
        ("RAG主服务", "http://localhost:9000/api/status"),
        ("数据管道服务", "http://localhost:9001/status")
    ]

    for name, url in services:
        try:
            response = requests.get(url, timeout=10)
            if response.status_code != 200:
                issues.append(f"{name} 返回状态码 {response.status_code}")
        except Exception as e:
            issues.append(f"{name} 连接失败: {e}")

    # 检查数据一致性
    try:
        sync_status = requests.get("http://localhost:9001/debug/sync-status", timeout=10)
        if sync_status.status_code == 200:
            data = sync_status.json()
            if data['to_add_count'] > 500:
                issues.append(f"待同步文章过多: {data['to_add_count']}")
            if data['cms_invalid_articles_count'] > data['cms_articles_count'] * 0.2:
                issues.append(f"无效文章比例过高: {data['cms_invalid_articles_count']}/{data['cms_articles_count']}")
    except Exception as e:
        issues.append(f"同步状态检查失败: {e}")

    # 检查查询性能
    try:
        start_time = time.time()
        query_response = requests.post(
            "http://localhost:9000/api/query",
            json={"query": "健康检查测试", "max_results": 1},
            timeout=30
        )
        query_time = time.time() - start_time

        if query_time > 10:
            issues.append(f"查询响应时间过长: {query_time:.2f}秒")
        elif query_response.status_code != 200:
            issues.append(f"查询接口异常: {query_response.status_code}")
    except Exception as e:
        issues.append(f"查询性能检查失败: {e}")

    return issues

def send_alert(issues):
    """发送告警邮件"""
    if not issues:
        return

    subject = "RAG系统健康检查告警"
    body = "发现以下问题:\n\n" + "\n".join(f"- {issue}" for issue in issues)

    # 配置邮件发送（根据实际情况修改）
    # msg = MIMEText(body)
    # msg['Subject'] = subject
    # msg['From'] = '<EMAIL>'
    # msg['To'] = '<EMAIL>'

    print(f"告警: {subject}")
    print(body)

if __name__ == "__main__":
    issues = health_check()
    if issues:
        send_alert(issues)
        print(f"发现 {len(issues)} 个问题")
    else:
        print("系统状态正常")
```

### 2. 定期维护任务
```bash
#!/bin/bash
# 定期维护脚本

echo "开始定期维护任务..."

# 1. 清理日志文件（保留最近30天）
find ./logs -name "*.log" -mtime +30 -delete

# 2. 备份数据库
mysqldump -u username -p database_name > backup_$(date +%Y%m%d).sql

# 3. 检查磁盘空间
df -h | grep -E "(80%|90%|100%)" && echo "警告: 磁盘空间不足"

# 4. 重启服务（如果需要）
# systemctl restart rag-service
# systemctl restart pipeline-service

echo "维护任务完成"
```

## 📚 总结

本文档详细介绍了RAG应用的双服务架构和所有API接口的运行逻辑。主要特点包括：

### 🎯 核心功能
- **智能问答**: 基于自定义提示词的高质量回答生成
- **文档管理**: 完整的文档上传、删除、更新流程
- **数据同步**: CMS与RAG系统的智能同步机制
- **无效文章处理**: 避免404错误的友好处理方式

### 🔧 技术亮点
- **微服务架构**: 主服务(9000)和数据管道(9001)分离
- **时间戳比较**: 微秒级精度的文档更新检测
- **错误处理**: 完善的异常处理和用户友好提示
- **调试支持**: 丰富的调试接口和状态监控

### 📈 性能优化
- **向量检索**: 基于ChromaDB的高效相似度搜索
- **分块处理**: 智能文档分块和索引管理
- **缓存机制**: 支持查询结果缓存
- **批量操作**: 高效的批量文档处理

### 🔒 安全保障
- **CORS配置**: 严格的跨域访问控制
- **请求限制**: API调用频率限制
- **数据验证**: 完整的输入验证和错误处理
- **日志安全**: 避免敏感信息泄露

### 🛠️ 运维支持
- **健康检查**: 自动化的系统状态监控
- **故障诊断**: 详细的问题排查指南
- **性能监控**: 查询响应时间和系统负载监控
- **维护脚本**: 自动化的日常维护任务

通过本文档，开发者可以全面了解系统架构、API使用方法、故障排除和最佳实践，确保RAG应用的稳定运行和持续优化。