# 数据管道API服务配置
DATA_PIPELINE_HOST=0.0.0.0
DATA_PIPELINE_PORT=9001

# 数据库配置 (ChestnutCMS)
DB_USER=root
DB_PASSWORD=5Secsgo100
DB_HOST=localhost
DB_PORT=3306
DB_NAME=chestnut_cms

# 网站配置
SITE_BASE_URL=http://*************

# RAG服务配置 - 指向本项目的端口9000
RAG_SERVICE_URL=http://localhost:9000

# 数据处理配置
MAX_CONTENT_LENGTH=10485760  # 10MB
SUPPORTED_FILE_TYPES=.txt,.html,.htm
CHUNK_SIZE=1000
CHUNK_OVERLAP=200

# 日志配置
LOG_LEVEL=INFO
LOG_FORMAT=%(asctime)s - %(name)s - %(levelname)s - %(message)s

# 安全配置
CORS_ORIGINS=["http://localhost:9000", "http://localhost:9001", "https://gzmdrw.cn"]  # 生产环境中应该设置具体的域名
RATE_LIMIT_PER_MINUTE=100 