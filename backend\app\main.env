# OpenAI API配置
OPENAI_API_KEY=sk-8n6aBFIP2OWSsh2n5wmLPBy8oVQSwR8avUPCrI7cUEBS1X4i
OPENAI_BASE_URL=https://api.openai-proxy.org/v1
OPENAI_MODEL=gpt-4o-mini
EMBEDDING_MODEL=text-embedding-3-small

# 应用配置
APP_HOST=0.0.0.0
APP_PORT=9000
DATA_DIR=./data
STORAGE_DIR=./storage
COLLECTION_NAME=documents

# ChromaDB配置 - 相对于项目根目录
CHROMA_PERSIST_DIRECTORY=./storage

# CORS配置 - 支持跨域访问
ALLOWED_ORIGINS=["http://localhost:3000", "http://localhost:9001", "http://localhost:9000", "http://localhost:8080","https://chat.example.org", "https://www.gzmdrw.cn"] 
